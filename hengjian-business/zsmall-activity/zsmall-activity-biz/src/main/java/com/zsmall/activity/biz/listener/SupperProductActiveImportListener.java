package com.zsmall.activity.biz.listener;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.core.utils.trans.TransactionUtils;
import com.hengjian.common.excel.core.ExcelListener;
import com.hengjian.common.excel.core.ExcelResult;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivity;
import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivityImportDTO;
import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivityPrice;
import com.zsmall.activity.entity.domain.dto.productActivity.SupplierProductActivityStock;
import com.zsmall.activity.entity.iservice.ISupplierProductActivityPriceService;
import com.zsmall.activity.entity.iservice.ISupplierProductActivityService;
import com.zsmall.activity.entity.iservice.ISupplierProductActivityStockService;
import com.zsmall.activity.entity.util.ProductActiveCodeUtil;
import com.zsmall.common.domain.LocaleMessage;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.common.enums.downloadRecord.RecordStateEnum;
import com.zsmall.common.enums.product.ShelfStateEnum;
import com.zsmall.common.enums.product.SupportedLogisticsEnum;
import com.zsmall.common.enums.productActivity.ProductActiveImportErrorEnum;
import com.zsmall.common.enums.productActivity.ProductActivityStateEnum;
import com.zsmall.common.enums.productActivity.ProductActivityTypeEnum;
import com.zsmall.extend.es.entity.EsProduct;
import com.zsmall.extend.es.esmapper.EsProductMapper;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.dto.stock.SkuStock;
import com.zsmall.product.entity.iservice.IProductService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.product.entity.iservice.IProductSkuStockService;
import com.zsmall.system.entity.domain.DownloadRecord;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.iservice.IDownloadRecordService;
import com.zsmall.system.entity.mapper.SiteCountryCurrencyMapper;
import com.zsmall.warehouse.entity.domain.Warehouse;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025年7月15日  11:26
 * @description: 供应商活动导入监听器
 */

@Slf4j
public class SupperProductActiveImportListener extends AnalysisEventListener<SupplierProductActivityImportDTO> implements ExcelListener<SupplierProductActivityImportDTO> {

    private ISupplierProductActivityService supplierProductActivityService;
    private ISupplierProductActivityStockService supplierProductActivityStockService;
    private ISupplierProductActivityPriceService supplierProductActivityPriceService;
    private  EsProductMapper esProductMapper;
    private SiteCountryCurrencyMapper siteCountryCurrencyMapper;
    private IProductSkuService productSkuService;
    private IProductService productService;
    private IDownloadRecordService iDownloadRecordService;
    private IProductSkuStockService productSkuStockService;
    private IWarehouseService warehouseService;
    private TransactionUtils transactionUtils;
    private String fileName;
    private String fileSize;

    private List<SupplierProductActivityImportDTO> supplierProductActivityImportDTOS  = new ArrayList<>();

    // 用于跟踪数据和行号的列表
    private List<DataWithRowNumber> dataWithRowNumbers = new ArrayList<>();

    /**
     * 导入记录状态
     */
    private RecordStateEnum recordState = RecordStateEnum.Generating;

    private LocaleMessage localeMessage = new LocaleMessage();

    /**
     * 添加带行号的错误消息
     * @param rowNumber 行号
     * @param errorEnum 错误枚举
     * @param message 错误消息
     */
    private void appendErrorWithRowNumber(int rowNumber, ProductActiveImportErrorEnum errorEnum, String message) {
        String errorMessage = String.format("【第%d行】数据异常：%s\n", rowNumber, message);
        localeMessage.append(errorEnum.args(errorMessage));
        recordState = RecordStateEnum.Failed;
    }

    /**
     * 添加不带行号的错误消息（用于全局验证）
     * @param errorEnum 错误枚举
     * @param message 错误消息
     */
    private void appendError(ProductActiveImportErrorEnum errorEnum, String message) {
        String errorMessage = String.format("%s\n", message);
        localeMessage.append(errorEnum.args(errorMessage));
        recordState = RecordStateEnum.Failed;
    }

    // 内部类用于跟踪数据和行号
    private static class DataWithRowNumber {
        private final SupplierProductActivityImportDTO data;
        private final int rowNumber;

        public DataWithRowNumber(SupplierProductActivityImportDTO data, int rowNumber) {
            this.data = data;
            this.rowNumber = rowNumber;
        }

        public SupplierProductActivityImportDTO getData() {
            return data;
        }

        public int getRowNumber() {
            return rowNumber;
        }
    }


    public SupperProductActiveImportListener(ISupplierProductActivityService supplierProductActivityService,
                                             ISupplierProductActivityStockService supplierProductActivityStockService,
                                             ISupplierProductActivityPriceService supplierProductActivityPriceService,
                                             IProductSkuService iProductSkuService,
                                             IDownloadRecordService iDownloadRecordService,
                                             IProductService productService,
                                             SiteCountryCurrencyMapper siteCountryCurrencyMapper,
                                             IProductSkuStockService productSkuStockService,
                                             IWarehouseService warehouseService,
                                             EsProductMapper esProductMapper,
                                             TransactionUtils transactionUtils,
                                             String fileName,
                                             String fileSize) {
        this.supplierProductActivityService = supplierProductActivityService;
        this.supplierProductActivityStockService = supplierProductActivityStockService;
        this.supplierProductActivityPriceService = supplierProductActivityPriceService;
        this.productSkuService = iProductSkuService;
        this.iDownloadRecordService = iDownloadRecordService;
        this.productService = productService;
        this.siteCountryCurrencyMapper = siteCountryCurrencyMapper;
        this.productSkuStockService = productSkuStockService;
        this.warehouseService = warehouseService;
        this.esProductMapper = esProductMapper;
        this.transactionUtils = transactionUtils;
        this.fileName = fileName;
        this.fileSize = fileSize;
    }

    @Override
    public ExcelResult<SupplierProductActivityImportDTO> getExcelResult() {
        return null;
    }

    @Override
    public void invoke(SupplierProductActivityImportDTO importDTO, AnalysisContext analysisContext) {
        // 记录数据和行号信息（Excel行号从1开始，但这里需要加1因为有表头）
        int rowNumber = analysisContext.readRowHolder().getRowIndex() + 1;
        dataWithRowNumbers.add(new DataWithRowNumber(importDTO, rowNumber));
    }

    @Transactional
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 执行跨行数据一致性验证
        validateSkuSiteConsistency();
        //校验仓库库存问题
        validateWarehouseStockConsistency();
        //因为上面已经分组完了，表格前面的数据一致，只需要校验一次，仓库数据库存数据每次循环校验
        SupplierProductActivityImportDTO importDTO = dataWithRowNumbers.get(0).getData();
        int firstRowNumber = dataWithRowNumbers.get(0).getRowNumber();

        // 检查必填字段
        if(StringUtils.isEmpty(importDTO.getActivityName())){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.FIELD_CANNOT_EMPTY, "活动名称");
        }
        if(StringUtils.isEmpty(importDTO.getProductSkuCode())){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.FIELD_CANNOT_EMPTY, "活动SKU ID");
        }
        if(StringUtils.isEmpty(importDTO.getSite())){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.FIELD_CANNOT_EMPTY, "SKU 站点");
        }
        if(ObjectUtil.isEmpty(importDTO.getSupplierActivityUnitPrice())){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.FIELD_CANNOT_EMPTY, "活动单价");
        }
        if(ObjectUtil.isEmpty(importDTO.getSupplierActivityOperationFee())){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.FIELD_CANNOT_EMPTY, "活动操作费");
        }
        if(ObjectUtil.isEmpty(importDTO.getSupplierActivityFinalDeliveryFee())){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.FIELD_CANNOT_EMPTY, "活动尾程派送费");
        }
        if(ObjectUtil.isEmpty(importDTO.getQuantityMinimum())){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.FIELD_CANNOT_EMPTY, "最小起订量(件)");
        }
        if(ObjectUtil.isEmpty(importDTO.getSupplierActivityStorageFee())){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.FIELD_CANNOT_EMPTY, "仓储费（每天/每件）");
        }
        if(ObjectUtil.isEmpty(importDTO.getFreeStoragePeriod())){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.FIELD_CANNOT_EMPTY, "免仓期（天）");
        }
        if(ObjectUtil.isEmpty(importDTO.getActivityDay())){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.FIELD_CANNOT_EMPTY, "活动期限（天）");
        }

        if(StringUtils.isEmpty(importDTO.getSupportedLogistics())){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.FIELD_CANNOT_EMPTY, "发货方式");
        }
        if(StringUtils.isEmpty(importDTO.getWarehouseCode())){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.FIELD_CANNOT_EMPTY, "仓库编号");
        }
        if(ObjectUtil.isEmpty(importDTO.getQuantityTotal())){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.FIELD_CANNOT_EMPTY, "可锁定库存数");
        }
        //业务校验
        if (importDTO.getActivityName().length()>20){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.FIELD_LENGTH_MAX, "活动名称最大长度为20个字符");
        }
        if (importDTO.getActivityDay()<30){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.FIELD_FORMAT_ERROR, "活动期限最小为30天");
        }
        if(importDTO.getQuantityMinimum()<=0){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.FIELD_FORMAT_ERROR, "最小起订量必须大于0");
        }
        if(importDTO.getQuantityTotal()<1){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.FIELD_CANNOT_EMPTY, "可锁定库存数最小为1");
        }
        if(importDTO.getFreeStoragePeriod()<=0){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.FIELD_CANNOT_EMPTY, "免仓期必须大于等于0");
        }

        LambdaQueryWrapper<SiteCountryCurrency> q = new LambdaQueryWrapper<>();
        q.eq(SiteCountryCurrency::getCountryCode, importDTO.getSite());
        SiteCountryCurrency siteCountryCurrency = siteCountryCurrencyMapper.selectOne(q);
        if (ObjectUtil.isEmpty(siteCountryCurrency)){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.OBJECT_NOT_EXIST, "未找到SKU 站点");
        }
        ProductSku productSku = TenantHelper.ignore(() -> productSkuService.queryByProductSkuCode(importDTO.getProductSkuCode()));
        if(ObjectUtil.isEmpty(productSku)){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.OBJECT_NOT_EXIST, "SKU ID不存在");
        }else {
            //校验下架
            if (ObjectUtil.notEqual(productSku.getShelfState(), ShelfStateEnum.OnShelf)){
                appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.OBJECT_NOT_EXIST, "SKU 已下架");
            }
        }

        Product product = TenantHelper.ignore(() -> productService.queryByProductSkuCode(importDTO.getProductSkuCode()));
        if(ObjectUtil.isEmpty(product)){
            appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.OBJECT_NOT_EXIST, "商品不存在");
        }else {
            if (ObjectUtil.notEqual(product.getShelfState(), ShelfStateEnum.OnShelf)){
                appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.OBJECT_NOT_EXIST, "产品 已下架");
            }
//            if (ObjectUtil.notEqual(product.getSupportedLogistics(), SupportedLogisticsEnum.All)){
//                if (ObjectUtil.notEqual(product.getSupportedLogistics().name(), importDTO.getSupportedLogistics())){
//                    appendErrorWithRowNumber(firstRowNumber, ProductActiveImportErrorEnum.OBJECT_NOT_EXIST, "发货方式不存在");
//                }
//            }
            //循环校验表格仓库、库存数据
            dataWithRowNumbers.forEach(s->{
                SupplierProductActivityImportDTO data = s.getData();
//            if (data.getQuantityMinimum()>data.getQuantityTotal()){
//                appendErrorWithRowNumber(s.getRowNumber(), ProductActiveImportErrorEnum.FIELD_FORMAT_ERROR, "可锁定库存数不能小于最小起订量");
//            }
                int currentRowNumber = s.getRowNumber();
                //校验发货方式
                if (ObjectUtil.notEqual(product.getSupportedLogistics(), SupportedLogisticsEnum.All)){
                    if (ObjectUtil.notEqual(product.getSupportedLogistics().name(), data.getSupportedLogistics())){
                        appendErrorWithRowNumber(currentRowNumber, ProductActiveImportErrorEnum.OBJECT_NOT_EXIST, "SKU 不支持该发货方式");
                    }
                }

                //先查仓库
                Warehouse warehouse = TenantHelper.ignore(()->warehouseService.getBaseMapper().selectOne(Wrappers.lambdaQuery(Warehouse.class)
                                                                                                                 .eq(Warehouse::getWarehouseCode, data.getWarehouseCode()).eq(Warehouse::getTenantId,  productSku.getTenantId())));
                if (ObjectUtil.isEmpty(warehouse)){
                    appendErrorWithRowNumber(currentRowNumber, ProductActiveImportErrorEnum.OBJECT_NOT_EXIST, "[仓库编码]不存在");
                }else {
                    data.setWarehouseSystemCode(warehouse.getWarehouseSystemCode());
                    //考虑单仓的情况
                    SkuStock skuStock = productSkuStockService.getBaseMapper()
                                                              .getSkuStockBySiteAndWarehouse(data.getProductSkuCode(), importDTO.getSite(), warehouse.getWarehouseSystemCode());
                    if (ObjectUtil.isEmpty(skuStock)){
                        appendErrorWithRowNumber(currentRowNumber, ProductActiveImportErrorEnum.OBJECT_NOT_EXIST, "仓库无库存");
                    }else {
                        if (ObjectUtil.equals(data.getSupportedLogistics(), SupportedLogisticsEnum.PickUpOnly.name())){
                            if (skuStock.getPickUpStockTotal() < data.getQuantityTotal()){
                                appendErrorWithRowNumber(currentRowNumber, ProductActiveImportErrorEnum.FIELD_FORMAT_ERROR,
                                    StrUtil.format("发货方式:{},仓库编码:{},可锁定库存数不能大于仓库库存总数",data.getSupportedLogistics(),warehouse.getWarehouseName()));
                            }
                        }
                        //如果是代发(考虑单仓)
                        if (StrUtil.equals(data.getSupportedLogistics(), SupportedLogisticsEnum.DropShippingOnly.name())){
                            if (skuStock.getDropShippingStockTotal()  < data.getQuantityTotal()){
                                appendErrorWithRowNumber(currentRowNumber, ProductActiveImportErrorEnum.FIELD_FORMAT_ERROR,
                                    StrUtil.format("发货方式:{},仓库编码:{},可锁定库存数不能大于仓库库存总数",data.getSupportedLogistics(),warehouse.getWarehouseName()));
                            }
                        }
                        data.setProductSkuStockId(skuStock.getId());

                    }
                }
            });
        }

        // 如果验证失败，保存错误记录并返回
        if(recordState.equals(RecordStateEnum.Failed)){
            DownloadRecord newRecord = new DownloadRecord();
            newRecord.setRecordState(RecordStateEnum.Failed);
            newRecord.setFileName(fileName);
            newRecord.setFileSize(fileSize);
            // TODO: 需要在DownloadTypePlusEnum中添加供应商活动导入类型
            newRecord.setDownloadType(DownloadTypePlusEnum.SupplierProductActivityListImport);
            newRecord.setImportMessage(localeMessage.toJSON());
            iDownloadRecordService.save(newRecord);
            return;
        }

        Map<String, Integer> logisticsQuantityMap = dataWithRowNumbers.stream()
                                                                   .map(DataWithRowNumber::getData)
                                                                   .filter(dto -> dto.getQuantityTotal() != null)
                                                                   .collect(Collectors.groupingBy(
                                                                       SupplierProductActivityImportDTO::getSupportedLogistics,
                                                                       Collectors.summingInt(dto -> dto.getQuantityTotal())
                                                                   ));

        Integer pickUpLocked = logisticsQuantityMap.getOrDefault(SupportedLogisticsEnum.PickUpOnly.name(), 0);
        Integer dropShippingLocked = logisticsQuantityMap.getOrDefault(SupportedLogisticsEnum.DropShippingOnly.name(), 0);
        //构建活动信息
        SupplierProductActivity sa = new SupplierProductActivity();
        long id = IdUtil.getSnowflakeNextId();
        String code = LoginHelper.getTenantId() + "-" + ProductActiveCodeUtil.generate();
        sa.setId(id);
        sa.setSupplierTenantId(LoginHelper.getTenantId());
        sa.setActivityName(importDTO.getActivityName());
        sa.setActivityType(ProductActivityTypeEnum.StockLock.name());
        sa.setActivityState(ProductActivityStateEnum.PendingReview.name());
        sa.setProductName(productSku.getName());
        LambdaEsQueryWrapper<EsProduct> esQueryWrapper = new LambdaEsQueryWrapper<>();
        esQueryWrapper.match(EsProduct::getSkuCode, importDTO.getProductSkuCode());
        EsProduct esProduct = esProductMapper.selectOne(esQueryWrapper);
        if (ObjectUtil.isNotEmpty(esProduct)){
            sa.setProductImg(esProduct.getOriginImageShowUrl());
        }
        sa.setProductSkuCode(importDTO.getProductSkuCode());
        sa.setProductCode(productSku.getProductCode());
        sa.setProductSku(productSku.getSku());
        sa.setSite(importDTO.getSite());
        sa.setCurrencySymbol(siteCountryCurrency.getCurrencySymbol());
        sa.setActivityDay(importDTO.getActivityDay());
        sa.setFreeStoragePeriod(importDTO.getFreeStoragePeriod());
        sa.setQuantityMinimum(importDTO.getQuantityMinimum());
        sa.setSupplierActivityCode(code);
        sa.setSupplierActivityStorageFee(importDTO.getSupplierActivityStorageFee());
        //锁库存总数= 每个仓库的锁货数量相加
        sa.setPickupQuantityLocked(pickUpLocked);
        sa.setDropShippingQuantityLocked(dropShippingLocked);
        sa.setPickupLockedUsed(0);
        sa.setDropShippingLockedUsed(0);
        // 开启事务
        TransactionStatus transactionStatus = transactionUtils.beginTransaction();
        try {
            // 保存活动信息
            supplierProductActivityService.save(sa);
            //处理价格
            SupplierProductActivityPrice spap = new SupplierProductActivityPrice();
            spap.setId(IdUtil.getSnowflakeNextId());
            spap.setSupplierTenantId(LoginHelper.getTenantId());
            spap.setSupplierActivityCode(code);
            spap.setSupplierActivityId(id);
            spap.setSupplierActivityUnitPrice(importDTO.getSupplierActivityUnitPrice());
            spap.setSupplierActivityOperationFee(importDTO.getSupplierActivityOperationFee());
            spap.setSupplierActivityFinalDeliveryFee(importDTO.getSupplierActivityFinalDeliveryFee());
            spap.setSupplierActivityPickUpPrice(importDTO.getSupplierActivityUnitPrice().add(importDTO.getSupplierActivityOperationFee()));
            spap.setSupplierActivityDropShippingPrice(importDTO.getSupplierActivityUnitPrice().add(importDTO.getSupplierActivityOperationFee()).add(importDTO.getSupplierActivityFinalDeliveryFee()));
            supplierProductActivityPriceService.save(spap);
            //处理库存
            dataWithRowNumbers.forEach(s->{
                SupplierProductActivityImportDTO dto = s.getData();
                SupplierProductActivityStock spas = new SupplierProductActivityStock();
                spas.setId(IdUtil.getSnowflakeNextId());
                spas.setSupplierActivityId(id);
                spas.setSupplierActivityCode(code);
                spas.setProductSkuStockId(dto.getProductSkuStockId());
                spas.setWarehouseCode(dto.getWarehouseCode());
                spas.setWarehouseSystemCode(dto.getWarehouseSystemCode());
                spas.setSupportedLogistics(dto.getSupportedLogistics());
                spas.setQuantityTotal(dto.getQuantityTotal());
                spas.setQuantitySold(0);
                spas.setQuantitySurplus(dto.getQuantityTotal());
                supplierProductActivityStockService.save(spas);
            });

            //插入成功日志模块
            DownloadRecord newRecord = new DownloadRecord();
            newRecord.setRecordState(RecordStateEnum.Ready);
            newRecord.setFileName(fileName);
            newRecord.setFileSize(fileSize);
            newRecord.setDownloadType(DownloadTypePlusEnum.SupplierProductActivityListImport);
            iDownloadRecordService.save(newRecord);
            // 提交事务
            transactionUtils.commitTransaction();
        } catch (Exception e) {
            // 回滚事务
            transactionUtils.rollbackTransaction();
            // 记录错误日志
            log.error("供应商活动导入数据保存失败，文件[{}]", fileName, e);

            // 保存失败的DownloadRecord
            DownloadRecord errorRecord = new DownloadRecord();
            errorRecord.setRecordState(RecordStateEnum.Failed);
            errorRecord.setFileName(fileName);
            errorRecord.setFileSize(fileSize);
            errorRecord.setDownloadType(DownloadTypePlusEnum.SupplierProductActivityListImport);

            // 构建错误信息
            LocaleMessage errorMessage = new LocaleMessage();
            errorMessage.append(ProductActiveImportErrorEnum.FIELD_FORMAT_ERROR.args("数据保存失败：" + e.getMessage()));
            errorRecord.setImportMessage(errorMessage.toJSON());
            // 保存错误记录（这个操作不在事务中，确保错误日志能保存成功）
            try {
                iDownloadRecordService.save(errorRecord);
            } catch (Exception saveLogException) {
                log.error("保存错误日志失败", saveLogException);
            }
        }
    }


    /**
     * 验证所有行的字段一致性
     * 需要验证的字段：活动名称,活动SKU ID,SKU站点,活动单价，操作费，尾程，最小起订量，仓储费，免仓期，活动期限
     */
    private void validateSkuSiteConsistency() {
        if (dataWithRowNumbers.isEmpty() || dataWithRowNumbers.size() <= 1) {
            return;
        }

        // 以第一行作为基准进行比较
        DataWithRowNumber baseData = dataWithRowNumbers.get(0);

        // 验证所有行与第一行的字段一致性
        for (int i = 1; i < dataWithRowNumbers.size(); i++) {
            DataWithRowNumber currentData = dataWithRowNumbers.get(i);

            // 校验活动名称
            validateSingleFieldConsistency(baseData, currentData, "活动名称",
                SupplierProductActivityImportDTO::getActivityName);

            // 校验活动SKU ID
            validateSingleFieldConsistency(baseData, currentData, "活动SKU ID",
                SupplierProductActivityImportDTO::getProductSkuCode);

            // 校验SKU站点
            validateSingleFieldConsistency(baseData, currentData, "SKU站点",
                SupplierProductActivityImportDTO::getSite);

            // 校验活动单价
            validateSingleFieldConsistency(baseData, currentData, "活动单价",
                SupplierProductActivityImportDTO::getSupplierActivityUnitPrice);

            // 校验操作费
            validateSingleFieldConsistency(baseData, currentData, "操作费",
                SupplierProductActivityImportDTO::getSupplierActivityOperationFee);

            // 校验尾程
            validateSingleFieldConsistency(baseData, currentData, "尾程",
                SupplierProductActivityImportDTO::getSupplierActivityFinalDeliveryFee);

            // 校验最小起订量
            validateSingleFieldConsistency(baseData, currentData, "最小起订量",
                SupplierProductActivityImportDTO::getQuantityMinimum);

            // 校验仓储费
            validateSingleFieldConsistency(baseData, currentData, "仓储费",
                SupplierProductActivityImportDTO::getSupplierActivityStorageFee);

            // 校验免仓期
            validateSingleFieldConsistency(baseData, currentData, "免仓期",
                SupplierProductActivityImportDTO::getFreeStoragePeriod);

            // 校验活动期限
            validateSingleFieldConsistency(baseData, currentData, "活动期限",
                SupplierProductActivityImportDTO::getActivityDay);
        }
    }


    /**
     * 验证仓库相关数据一致性
     * 需求1：针对同一个活动SKU ID+SKU站点+发货方式+仓库编号，可锁定库存数不允许出现不同的值
     * 需求2：同一个活动SKU ID+SKU站点+发货方式 仓库编码不能重复
     */
    private void validateWarehouseStockConsistency() {
        // 需求2：检查仓库编码重复
        validateWarehouseCodeDuplication();

        // 需求1：检查库存数量一致性
        validateStockQuantityConsistency();
    }

    /**
     * 需求2：验证同一个活动SKU ID+SKU站点+发货方式下仓库编码不能重复
     */
    private void validateWarehouseCodeDuplication() {
        // 按Activity SKU ID + SKU Site + Shipping Method分组
        Map<String, List<DataWithRowNumber>> groupedBySkuSiteLogistics = new HashMap<>();

        for (DataWithRowNumber dataWithRow : dataWithRowNumbers) {
            SupplierProductActivityImportDTO data = dataWithRow.getData();
            String groupKey = data.getProductSkuCode() + "_" + data.getSite() + "_" + data.getSupportedLogistics();
            groupedBySkuSiteLogistics.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(dataWithRow);
        }

        // 检查每个分组内的仓库编码是否重复
        for (Map.Entry<String, List<DataWithRowNumber>> entry : groupedBySkuSiteLogistics.entrySet()) {
            List<DataWithRowNumber> groupData = entry.getValue();
            if (groupData.size() <= 1) {
                continue; // 只有一条数据，无需验证
            }

            // 使用Set检查仓库编码重复
            Set<String> warehouseCodes = new HashSet<>();
            for (DataWithRowNumber dataWithRow : groupData) {
                String warehouseCode = dataWithRow.getData().getWarehouseCode();
                if (!warehouseCodes.add(warehouseCode)) {
                    // 发现重复的仓库编码
                    appendErrorWithRowNumber(dataWithRow.getRowNumber(),
                        ProductActiveImportErrorEnum.FIELD_REPEAT, "仓库编码重复");
                    return; // 发现重复就立即返回
                }
            }
        }
    }

    /**
     * 需求1：验证相同Activity SKU ID + SKU Site + Shipping Method + Warehouse Code的库存数量一致性
     */
    private void validateStockQuantityConsistency() {
        // 按Activity SKU ID + SKU Site + Shipping Method + Warehouse Code分组
        Map<String, List<DataWithRowNumber>> groupedData = new HashMap<>();

        for (DataWithRowNumber dataWithRow : dataWithRowNumbers) {
            SupplierProductActivityImportDTO data = dataWithRow.getData();
            String groupKey = data.getProductSkuCode() + "_" + data.getSite() + "_" +
                            data.getSupportedLogistics() + "_" + data.getWarehouseCode();
            groupedData.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(dataWithRow);
        }

        // 验证每个分组内的库存数量一致性
        for (Map.Entry<String, List<DataWithRowNumber>> entry : groupedData.entrySet()) {
            List<DataWithRowNumber> groupData = entry.getValue();
            if (groupData.size() <= 1) {
                continue; // 只有一条数据，无需验证
            }

            // 获取第一个值作为基准
            int baseQuantity = groupData.get(0).getData().getQuantityTotal();

            // 检查其他值是否与基准值一致
            for (int i = 1; i < groupData.size(); i++) {
                DataWithRowNumber currentDataWithRow = groupData.get(i);
                int currentQuantity = currentDataWithRow.getData().getQuantityTotal();

                if (!Objects.equals(baseQuantity, currentQuantity)) {
                    // 发现库存数量不一致
                    appendErrorWithRowNumber(currentDataWithRow.getRowNumber(),
                        ProductActiveImportErrorEnum.WAREHOUSE_STOCK_INCONSISTENT, "可锁定仓库数有误，导入失败");
                    return; // 发现不一致就立即返回
                }
            }
        }
    }

    /**
     * 单个字段一致性验证方法
     */
    private <T> void validateSingleFieldConsistency(DataWithRowNumber baseData, DataWithRowNumber currentData,
                                                   String fieldName, Function<SupplierProductActivityImportDTO, T> fieldExtractor) {
        T baseValue = fieldExtractor.apply(baseData.getData());
        T currentValue = fieldExtractor.apply(currentData.getData());

        // 处理BigDecimal的比较
        boolean isEqual;
        if (baseValue instanceof BigDecimal && currentValue instanceof BigDecimal) {
            isEqual = ((BigDecimal) baseValue).compareTo((BigDecimal) currentValue) == 0;
        } else {
            isEqual = Objects.equals(baseValue, currentValue);
        }

        if (!isEqual) {
            String errorMessage = String.format("%s 存在不同的值", fieldName);
            appendErrorWithRowNumber(currentData.getRowNumber(), ProductActiveImportErrorEnum.FIELD_VALUE_INCONSISTENT, errorMessage);
        }
    }

    /**
     * 通用字段一致性验证方法（可指定错误类型）
     */
    private <T> void validateFieldConsistency(List<DataWithRowNumber> groupData, String fieldName,
                                            Function<SupplierProductActivityImportDTO, T> fieldExtractor,
                                            ProductActiveImportErrorEnum errorEnum) {
        if (groupData.size() <= 1) {
            return;
        }

        // 获取第一个值作为基准
        T baseValue = fieldExtractor.apply(groupData.get(0).getData());

        // 检查其他值是否与基准值一致
        for (int i = 1; i < groupData.size(); i++) {
            DataWithRowNumber currentDataWithRow = groupData.get(i);
            T currentValue = fieldExtractor.apply(currentDataWithRow.getData());

            // 处理BigDecimal的比较
            boolean isEqual;
            if (baseValue instanceof BigDecimal && currentValue instanceof BigDecimal) {
                isEqual = ((BigDecimal) baseValue).compareTo((BigDecimal) currentValue) == 0;
            } else {
                isEqual = Objects.equals(baseValue, currentValue);
            }

            if (!isEqual) {
                String errorMessage = String.format("%s 存在不同的值", fieldName);
                appendErrorWithRowNumber(currentDataWithRow.getRowNumber(), errorEnum, errorMessage);
                return; // 发现不一致就立即返回
            }
        }
    }

}
